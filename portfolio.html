<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إيهاب حسين - مطور فرونت إند</title>
    <link rel="stylesheet" href="portfolio-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo">
                <span class="logo-text">إيهاب حسين</span>
                <div class="logo-orbit"></div>
            </div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">الرئيسية</a></li>
                <li><a href="#about" class="nav-link">نبذة عني</a></li>
                <li><a href="#skills" class="nav-link">المهارات</a></li>
                <li><a href="#projects" class="nav-link">المشاريع</a></li>
                <li><a href="#contact" class="nav-link">تواصل معي</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="stars"></div>
        <div class="floating-elements">
            <div class="code-element element-1">&lt;/&gt;</div>
            <div class="code-element element-2">{}</div>
            <div class="code-element element-3">React</div>
            <div class="code-element element-4">JS</div>
            <div class="code-element element-5">CSS</div>
        </div>
        
        <div class="hero-content">
            <div class="profile-image">
                <div class="image-placeholder">
                    <i class="fas fa-user"></i>
                </div>
                <div class="image-glow"></div>
            </div>
            
            <h1 class="hero-title">
                <span class="title-line">إيهاب حسين</span>
                <span class="title-subtitle">مطور فرونت إند محترف</span>
            </h1>
            
            <p class="hero-description">
                أحول الأفكار إلى تجارب رقمية مذهلة باستخدام أحدث تقنيات الويب
            </p>
            
            <div class="hero-buttons">
                <button class="cta-button primary" id="viewWorkBtn">
                    <span class="button-text">استعرض أعمالي</span>
                    <div class="button-glow"></div>
                </button>
                <button class="cta-button secondary" id="contactBtn">
                    <span class="button-text">تواصل معي</span>
                </button>
            </div>
        </div>

        <div class="scroll-indicator">
            <div class="scroll-arrow"></div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">نبذة عني</h2>
            <div class="about-content">
                <div class="about-text">
                    <p class="about-intro">
                        مرحباً! أنا إيهاب حسين، مطور فرونت إند شغوف بإنشاء تجارب مستخدم استثنائية. 
                        أتخصص في تطوير واجهات المستخدم التفاعلية والمتجاوبة باستخدام أحدث التقنيات.
                    </p>
                    
                    <div class="experience-stats">
                        <div class="stat-item">
                            <div class="stat-number">3+</div>
                            <div class="stat-label">سنوات خبرة</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">50+</div>
                            <div class="stat-label">مشروع مكتمل</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">100%</div>
                            <div class="stat-label">رضا العملاء</div>
                        </div>
                    </div>
                </div>
                
                <div class="about-visual">
                    <div class="code-animation">
                        <div class="code-line">
                            <span class="code-keyword">const</span>
                            <span class="code-variable">developer</span>
                            <span class="code-operator">=</span>
                            <span class="code-string">"إيهاب حسين"</span>
                        </div>
                        <div class="code-line">
                            <span class="code-keyword">let</span>
                            <span class="code-variable">skills</span>
                            <span class="code-operator">=</span>
                            <span class="code-array">["React", "Vue", "JavaScript"]</span>
                        </div>
                        <div class="code-line">
                            <span class="code-keyword">function</span>
                            <span class="code-function">createAmazingUI</span>
                            <span class="code-brackets">()</span>
                            <span class="code-brackets">{</span>
                        </div>
                        <div class="code-line indent">
                            <span class="code-keyword">return</span>
                            <span class="code-string">"تجربة مستخدم رائعة"</span>
                        </div>
                        <div class="code-line">
                            <span class="code-brackets">}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="skills">
        <div class="container">
            <h2 class="section-title">مهاراتي التقنية</h2>
            <div class="skills-grid">
                <div class="skill-category">
                    <h3 class="category-title">
                        <i class="fab fa-js-square"></i>
                        لغات البرمجة
                    </h3>
                    <div class="skill-items">
                        <div class="skill-item">
                            <span class="skill-name">JavaScript</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="95%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">TypeScript</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="85%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">HTML5</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="98%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">CSS3</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="92%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="skill-category">
                    <h3 class="category-title">
                        <i class="fab fa-react"></i>
                        المكتبات والإطارات
                    </h3>
                    <div class="skill-items">
                        <div class="skill-item">
                            <span class="skill-name">React.js</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="90%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">Vue.js</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="80%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">Next.js</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="85%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">Sass/SCSS</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="88%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="skill-category">
                    <h3 class="category-title">
                        <i class="fas fa-tools"></i>
                        الأدوات والتقنيات
                    </h3>
                    <div class="skill-items">
                        <div class="skill-item">
                            <span class="skill-name">Git & GitHub</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="90%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">Webpack</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="75%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">Figma</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="85%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">Responsive Design</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="95%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="projects">
        <div class="container">
            <h2 class="section-title">مشاريعي المميزة</h2>
            <div class="projects-grid">
                <div class="project-card">
                    <div class="project-image">
                        <div class="project-overlay">
                            <div class="project-links">
                                <a href="#" class="project-link">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="#" class="project-link">
                                    <i class="fab fa-github"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3 class="project-title">تطبيق إدارة المهام</h3>
                        <p class="project-description">
                            تطبيق ويب تفاعلي لإدارة المهام اليومية مع واجهة مستخدم حديثة ومتجاوبة
                        </p>
                        <div class="project-tech">
                            <span class="tech-tag">React</span>
                            <span class="tech-tag">Redux</span>
                            <span class="tech-tag">CSS3</span>
                        </div>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-image">
                        <div class="project-overlay">
                            <div class="project-links">
                                <a href="#" class="project-link">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="#" class="project-link">
                                    <i class="fab fa-github"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3 class="project-title">متجر إلكتروني</h3>
                        <p class="project-description">
                            منصة تجارة إلكترونية كاملة مع نظام دفع آمن وواجهة إدارة متقدمة
                        </p>
                        <div class="project-tech">
                            <span class="tech-tag">Vue.js</span>
                            <span class="tech-tag">Node.js</span>
                            <span class="tech-tag">MongoDB</span>
                        </div>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-image">
                        <div class="project-overlay">
                            <div class="project-links">
                                <a href="#" class="project-link">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="#" class="project-link">
                                    <i class="fab fa-github"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3 class="project-title">لوحة تحكم تحليلية</h3>
                        <p class="project-description">
                            لوحة تحكم تفاعلية لعرض البيانات والإحصائيات مع رسوم بيانية متحركة
                        </p>
                        <div class="project-tech">
                            <span class="tech-tag">React</span>
                            <span class="tech-tag">D3.js</span>
                            <span class="tech-tag">TypeScript</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">تواصل معي</h2>
            <p class="contact-subtitle">هل لديك مشروع في ذهنك؟ دعنا نحوله إلى واقع!</p>
            
            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-details">
                            <h4>البريد الإلكتروني</h4>
                            <span><EMAIL></span>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-details">
                            <h4>رقم الهاتف</h4>
                            <span>+20 ************</span>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="contact-details">
                            <h4>الموقع</h4>
                            <span>القاهرة، مصر</span>
                        </div>
                    </div>
                </div>
                
                <div class="social-links">
                    <a href="#" class="social-link">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="fab fa-behance"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <script src="portfolio-script.js"></script>
</body>
</html>
